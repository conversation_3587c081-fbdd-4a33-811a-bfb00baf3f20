{"version": "0.2.0", "configurations": [{"name": "Debug Node.js C++ Addon", "type": "node", "request": "launch", "program": "${workspaceFolder}/js/example.js", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}, "preLaunchTask": "build-debug"}, {"name": "Debug Node.js with Inspector", "type": "node", "request": "launch", "program": "${workspaceFolder}/js/example.js", "console": "integratedTerminal", "port": 9229, "env": {"NODE_ENV": "development"}, "preLaunchTask": "build-debug"}, {"name": "Attach to C++ (lldb)", "type": "lldb", "request": "attach", "program": "${env:HOME}/.nvm/versions/node/v22.16.0/bin/node", "args": ["${workspaceFolder}/js/example.js"], "cwd": "${workspaceFolder}", "environment": [{"name": "NODE_ENV", "value": "development"}], "preLaunchTask": "build-debug"}, {"name": "Launch C++ with lldb", "type": "lldb", "request": "launch", "program": "${env:HOME}/.nvm/versions/node/v22.16.0/bin/node", "args": ["${workspaceFolder}/js/example.js"], "cwd": "${workspaceFolder}", "environment": [{"name": "NODE_ENV", "value": "development"}], "preLaunchTask": "build-debug", "stopOnEntry": false, "externalConsole": false}]}