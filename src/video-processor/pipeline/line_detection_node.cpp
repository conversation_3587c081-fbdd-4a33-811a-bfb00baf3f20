#include "./line_detection_node.h"
#include "../../signal-processing/find_front_frwd.h"
#include <algorithm>

namespace IQVideoProcessor::Pipeline {

LineDetectionNode::LineDetectionNode() {
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment*&& inWindow) {
  if (!inWindow || !running()) return false;

  // Run placeholder line detection within the bounds of this window
  (void)runLineDetection(*inWindow);

  // For now, forward the same pointer unchanged as both input and output type are DemodulatedWindow*
  if (!this->sendOutput(std::move(inWindow))) {
    stop();
    return false;
  }
  return running();
}

bool LineDetectionNode::runLineDetection(const DemodulatedSegment &window) {
  if (window.effectiveSamples < 3) return false;

  // Our algorithm expects the pointer to the effective region start, with sufficient
  // left/right padding in memory (window.data contains overlap guards around effective).
  const ComplexType* effectivePtr = window.data.data() + window.leftOverlapSamples;
  const std::size_t effectiveLen = window.effectiveSamples;

  // Map config to finder params
  const int frontType = config_.frontType;
  const std::size_t aveSize = static_cast<std::size_t>(config_.aveSize <= 1.0 ? 1 : static_cast<std::size_t>(config_.aveSize + 0.5));
  const std::size_t thrSize = static_cast<std::size_t>(config_.thresholdSize <= 1.0 ? 1 : static_cast<std::size_t>(config_.thresholdSize + 0.5));
  const ComplexType thrDelta = static_cast<ComplexType>(config_.thresholdTrigDelta);

  // External threshold ring buffer reused across calls
  static std::vector<ComplexType> ring;
  if (ring.size() < thrSize) ring.resize(thrSize);

  IQVideoProcessor::SignalProcessing::FindFrontFrwd<ComplexType> finder(effectivePtr, effectiveLen, ring);
  ComplexType frontPos = 0;
  bool frontFound = false;
  (void)aveSize; // keep local computation if needed elsewhere
  const bool ok = finder(
      /*toEOF*/ true,
      /*frontType*/ frontType,
      /*dblFromPosition*/ static_cast<ComplexType>(0),
      /*dblElements*/ static_cast<ComplexType>(0),
      /*dblAveSize*/ static_cast<ComplexType>(config_.aveSize),
      /*dblThresholdSize*/ static_cast<ComplexType>(config_.thresholdSize),
      /*dblThresholdTrigDelta*/ static_cast<ComplexType>(config_.thresholdTrigDelta),
      /*out frontPos*/ frontPos,
      /*out frontFound*/ frontFound);

  // For now, we only return boolean; future schema may propagate the index as metadata
  return ok && frontFound;
}

} // namespace IQVideoProcessor::Pipeline

