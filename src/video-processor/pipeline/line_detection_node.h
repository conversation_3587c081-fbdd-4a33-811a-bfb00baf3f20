#pragma once

#include "./iq_demodulation_node_types.h"
#include "../../stream-pipeline/stream_node.h"

namespace IQVideoProcessor::Pipeline {

// Temporary configuration for line detection; values are placeholders until finalized
struct LineDetectionConfig {
  // Sign of the front to detect: >0 rising, <0 falling, 0 either
  int frontType = 0;
  // Averaging window length (samples) used by the external algorithm concept; kept for future use
  double aveSize = 9.0;
  // Threshold window size for evaluating deltas (samples)
  double thresholdSize = 33.0;
  // Minimum trigger delta (absolute) to qualify as a front
  double thresholdTrigDelta = 0.15; // in demodulated units (float)
};

// Line detection node placeholder: consumes demodulated windows and (for now) forwards them unchanged.
// Prepared to host a ported version of the external find-front-frwd/bkwd algorithm operating on float data.
class LineDetectionNode final : public SPipeline::StreamNode<DemodulatedSegment*, DemodulatedSegment*> {
public:
  LineDetectionNode();
  ~LineDetectionNode() override;

  void setConfig(const LineDetectionConfig &cfg) { config_ = cfg; }

private:
  bool process(DemodulatedSegment*&& inWindow) override;

  // Placeholder for upcoming algorithm adaptation from external/find-front
  bool runLineDetection(const DemodulatedSegment &window);

  LineDetectionConfig config_{};
};

} // namespace IQVideoProcessor::Pipeline

