#if !defined(_FIND_FRONT_INCLUDED_)
#define _FIND_FRONT_INCLUDED_

#include "../processor/processor.h"

namespace Processing
{

class CFindFrontFrwdProcessor : private CProcessor
{
public:
    CFindFrontFrwdProcessor(CUniversalSource& source);
    bool operator()(bool toEOF, int frontType, double dblFromPosition, double dblElements, double dblAveSize, double dblThresholdSize, double dblThresholdTrigDelta, double& frontPos, bool& frontFound);    
};

class CFindFrontBkwdProcessor : private CProcessor
{
public:
    CFindFrontBkwdProcessor(CUniversalSource& source);
    bool operator()(bool toSOF, int frontType, double dblFromPosition, double dblElements, double dblAveSize, double dblThresholdSize, double dblThresholdTrigDelta, double& frontPos, bool& frontFound);    
};

} // namespace Processing

#endif