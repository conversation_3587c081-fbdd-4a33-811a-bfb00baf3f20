#include "find-front.h"
#include "../../gdefs.h"
#include "../processing-buffer.h"
#include "../helpers/helpers.h"
#include <math.h>

#define abs(x) ((x) > 0 ? (x) : -(x))
#define min(x, y) ((x) < (y) ? (x) : (y))

namespace Processing
{

CFindFrontFrwdProcessor::CFindFrontFrwdProcessor(CUniversalSource& source): CProcessor(source)
{

}

bool CFindFrontFrwdProcessor::operator()(bool toEOF, int frontType, double dblFromPosition, double dblElements, double dblAveSize, double dblThresholdSize, double dblThresholdTrigDelta, double& frontPos, bool& frontFound)
{
    frontFound = false;

    if (!Helpers::validatePosition(dblFromPosition, m_source))
        return false;

    dblFromPosition = m_source.translatePosFrwd(dblFromPosition);
    uPos sourceMaxPos = m_source.size() - 1;

    uSmSize aveSize, halfAveSize;
    if (!Helpers::getAveSizes(dblAveSize, aveSize, halfAveSize)) 
        return false;

    uSmSize thresholdSize;
    if (!Helpers::getThresholdSize(dblThresholdSize, thresholdSize))
        return false;

    double dblEndPosition = dblFromPosition + dblElements;
    uPos processingStartPos = static_cast<uPos>(dblFromPosition); 
    uSize processingSize = static_cast<uSmSize>(ceil(dblEndPosition)) - processingStartPos;
    Helpers::adaptFrwdProcessingFragment(toEOF, halfAveSize, sourceMaxPos, processingStartPos, processingSize, dblEndPosition);
    if (processingSize <= thresholdSize)
    {
        return true;
    }

    /**
     * At this point we have:
     * @uSmSize aveSize - average full size
     * @uSmSize halfAveSize - the half of the average size
     * @uSmSize thresholdSize - the size of the search threshold
     * @uSize processingSize - search fragment size (considering aveElements)
     * @uPos processingStartPos - startpos (considering aveElements)
     */

    Helpers::prepareFrwdThresholdAveData(m_source, processingStartPos, thresholdSize, aveSize, halfAveSize);

    uValue const* pData;
    uValueLarge* pProcessingBuff = reinterpret_cast<uValueLarge *>(ProcessingBuffer);
    uSize totalProcessed = thresholdSize - 1;

    const UValueParam uValParam = m_source.valueParam(); 
    uValueLarge searchedDelta = uValParam.dblValToUValue(dblThresholdTrigDelta);
    searchedDelta = abs(searchedDelta);

    uPos readPos = processingStartPos + thresholdSize;
    uSize unprocessed = processingSize - thresholdSize;
    uSmPos thresholdStartIndex = 0, thresholdEndIndex = thresholdSize - 1;

    if (aveSize > 1)
    {
        readPos -= halfAveSize + 1;
        searchedDelta *= aveSize;
        while (unprocessed > 0)
        {
            uSmSize processed = 0;
            uSmPos aveFirst = 0, aveLast = aveSize;
            uSmSize portion = static_cast<uSmSize>(min(unprocessed + aveSize, MAX_READ_SIZE));
            pData = m_source.getData(readPos, portion);

            while (aveLast < portion)
            {
                uValueLarge currentValue = pProcessingBuff[thresholdEndIndex++]; // Getting the previos end summ from the buffer + incrementing the end index
                uValueLarge previousValue = pProcessingBuff[thresholdStartIndex]; // Getting the start summ from the buffer
                currentValue -= pData[aveFirst++]; // Recalculating the new end summ from the previos one
                currentValue += pData[aveLast++];
                pProcessingBuff[thresholdStartIndex++] = currentValue; // Replacing the old start summ with new end summ + incrementing the start index
                processed++;
                
                if (thresholdStartIndex == thresholdSize)
                    thresholdStartIndex = 0;

                if (thresholdEndIndex == thresholdSize)
                    thresholdEndIndex = 0;

                uValueLarge delta = currentValue - previousValue;
                delta = abs(delta);
                
                if (delta >= searchedDelta)
                {
                    if ((frontType >= 0 && previousValue <= currentValue) || (frontType <= 0 && previousValue >= currentValue))
                    {
                        frontPos = static_cast<double>(processingStartPos + totalProcessed + processed) - static_cast<double>(thresholdSize) / 2.0;
                        if (frontPos > dblFromPosition && frontPos <= dblEndPosition)
                        {
                            frontFound = true;
                            frontPos = m_source.translatePosBkwd(frontPos);
                            return true;
                        }
                    }
                }
            }
            
            readPos += processed;
            totalProcessed += processed;
            unprocessed -= processed;
        }
    }
    else
    {
        while (unprocessed > 0)
        {
            uSmSize portion = static_cast<uSmSize>(min(unprocessed, MAX_READ_SIZE));
            pData = m_source.getData(readPos, portion);

            uSmSize processed = 0;
            while (processed < portion)
            {
                uValueLarge previousValue = pProcessingBuff[thresholdStartIndex]; // Getting the start summ from the buffer
                uValueLarge currentValue = pData[processed++];
                pProcessingBuff[thresholdStartIndex++] = currentValue; // Replacing the old start summ with new end summ + incrementing the start index
                thresholdEndIndex++;

                if (thresholdStartIndex == thresholdSize)
                    thresholdStartIndex = 0;

                if (thresholdEndIndex == thresholdSize)
                    thresholdEndIndex = 0;

                uValueLarge delta = currentValue - previousValue;
                delta = abs(delta);
                
                if (delta >= searchedDelta)
                {
                    if ((frontType >= 0 && previousValue <= currentValue) || (frontType <= 0 && previousValue >= currentValue))
                    {
                        frontPos = static_cast<double>(processingStartPos + totalProcessed + processed) - static_cast<double>(thresholdSize) / 2.0;
                        if (frontPos > dblFromPosition && frontPos <= dblEndPosition)
                        {
                            frontFound = true;
                            frontPos = m_source.translatePosBkwd(frontPos);
                            return true;
                        }
                    }
                }
            }
            
            readPos += processed;
            totalProcessed += processed;
            unprocessed -= processed;
        }
    }

    return true;
}

} // namespace Processing
