#pragma once

#include "../iiq-stream/iiq_stream.h"

namespace IQVideoProcessor {

// Public helper functions for chunk sizing
size_t calcProcessingChunkSize(SampleRateType sampleRate);
size_t calcProcessingOverlapSize(SampleRateType sampleRate);

class VideoProcessor {
public:
  explicit VideoProcessor(std::unique_ptr<IIQStream> stream);
  ~VideoProcessor();

  bool initialize();
  void shutdown();

private:
  std::unique_ptr<IIQStream> stream_;
  SampleRateType sampleRate_ = 0; // Sample rate of the video stream

  size_t maxVideoLineSamples_ = 0; // Maximum samples per video line
  bool initialized_;
};

} // namespace IQVideoProcessor
