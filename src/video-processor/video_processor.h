#pragma once

#include "../iiq-stream/iiq_stream.h"
#include <memory>
#include <thread>

// Forward declarations
namespace IQVideoProcessor::Pipeline {
  class IQAcquisitionNode;
  class IQAsyncBridge;
  class IQDemodulationNode;
  class LineDetectionNode;
}

namespace SPipeline {
  template<typename T> class PassthroughLink;
}

namespace IQVideoProcessor {

// Forward declaration for pipeline types
namespace Pipeline {
  struct DemodulatedSegment;
}

// Public helper functions for chunk sizing
size_t calcProcessingChunkSize(SampleRateType sampleRate);
size_t calcProcessingOverlapSize(SampleRateType sampleRate);

class VideoProcessor {
public:
  explicit VideoProcessor(std::unique_ptr<IIQStream> stream);
  ~VideoProcessor();

  bool initialize();
  void shutdown();
  void start(); // New method for starting worker thread

private:
  std::unique_ptr<IIQStream> stream_;
  SampleRateType sampleRate_ = 0; // Sample rate of the video stream

  size_t maxVideoLineSamples_ = 0; // Maximum samples per video line
  bool initialized_;

  // Pipeline components (pointer-based storage)
  std::unique_ptr<Pipeline::IQAcquisitionNode> acquisitionNode_;
  std::unique_ptr<Pipeline::IQAsyncBridge> asyncBridge_;
  std::unique_ptr<Pipeline::IQDemodulationNode> demodNode_;
  std::unique_ptr<SPipeline::PassthroughLink<Pipeline::DemodulatedSegment*>> demodPassthrough_;
  std::unique_ptr<Pipeline::LineDetectionNode> lineDetectionNode_;

  // Worker thread
  std::unique_ptr<std::thread> workerThread_;
};

} // namespace IQVideoProcessor
