#pragma once

// IQ Stream read / buffering configs
constexpr size_t IQ_STREAM_READ_SIZE                    = 8 * 1024;         // Reading the IQ stream in chunks of 8k samples
constexpr size_t IQ_STREAM_MIN_READ_BUFF_NUM            = 16;               // Minimum number of read buffers for IQ stream acquisition

// IQ Stream chunk configs
constexpr double MIN_LINE_RATE_HZ                       = 15000.0;          // Minimum line rate for video processing (auto)
constexpr size_t MIN_SAMPLES_PER_VIDEO_LINE             = 320;              // Minimum samples per video line for processing (topic for discussion)

// Number of video lines per processing chunk (controls per-window effectiveSamples)
constexpr double LINES_PER_CHUNK                        = 20.0;             // Put 20 lines per chunk for processing
