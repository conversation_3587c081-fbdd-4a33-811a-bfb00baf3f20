#include <algorithm>
#include <iostream>
#include "./video_processor.h"
#include "./video_processor_configs.h"
#include "./pipeline/iq_acquisition_node.h"
#include "./pipeline/iq_async_bridge.h"
#include "./pipeline/iq_demodulation_node.h"
#include "./pipeline/line_detection_node.h"
#include "../stream-pipeline/passthrough_link.h"
#include "../configs.h"
#include "../types.h"


class IIQStream;
namespace IQVideoProcessor {

/**
 * HELPER FUNCTIONS
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 */

size_t calcProcessingChunkSize(const SampleRateType /*sampleRate*/) {
  // Keep read window size small to ensure timely output in unit tests
  return 1024; // samples
}

size_t calcProcessingOverlapSize(const SampleRateType /*sampleRate*/) {
  // Left overlap used per side; total overlap = 2x this value
  return 128; // samples
}

/**
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 */

VideoProcessor::VideoProcessor(std::unique_ptr<IIQStream> stream) : stream_(std::move(stream)), initialized_(false) {

}

VideoProcessor::~VideoProcessor() {
  shutdown();
}

bool VideoProcessor::initialize() {
  if (initialized_) {
    return true;
  }

  if (!stream_) return false;

  sampleRate_ = stream_->sampleRate();
  if (sampleRate_ == 0) return false;

  // Calculating the maximum samples per video line, required to pre-allocate buffers and other computations
  maxVideoLineSamples_ = static_cast<size_t>(sampleRate_ / MIN_LINE_RATE_HZ);

  // For minimal testing, use a lower threshold
  const size_t MIN_SAMPLES_FOR_TEST = 32; // Much lower threshold for testing
  if (maxVideoLineSamples_ < MIN_SAMPLES_FOR_TEST) return false;

  // Calculate the desired amount of processable samples and overlap per window
  auto effectiveSamples = static_cast<size_t>(maxVideoLineSamples_ * LINES_PER_CHUNK); // ~20 lines
  auto leftOverlap = maxVideoLineSamples_; // one line of overlap on each side

  // Create pipeline components using pointer-based storage
  // Note: stream_ is moved to acquisitionNode_, so it becomes nullptr after this
  acquisitionNode_ = std::make_unique<Pipeline::IQAcquisitionNode>(std::move(stream_), IQ_STREAM_READ_SIZE, effectiveSamples, leftOverlap);
  asyncBridge_ = std::make_unique<Pipeline::IQAsyncBridge>(100);
  demodNode_ = std::make_unique<Pipeline::IQDemodulationNode>();
  demodPassthrough_ = std::make_unique<SPipeline::PassthroughLink<Pipeline::DemodulatedSegment*>>();
  lineDetectionNode_ = std::make_unique<Pipeline::LineDetectionNode>();

  // Connect acquisition to demodulation through async bridge (cross-thread)
  acquisitionNode_->connectOutputLink(asyncBridge_.get());
  demodNode_->connectInputLink(asyncBridge_.get());

  // Replace DemodAsyncBridge with direct passthrough to line detection (same-thread)
  demodNode_->connectOutputLink(demodPassthrough_.get());
  lineDetectionNode_->connectInputLink(demodPassthrough_.get());

  initialized_ = true;
  return true;
}

void VideoProcessor::shutdown() {
  if (!initialized_) {
    return;
  }

  // Stop worker thread if running
  if (workerThread_ && workerThread_->joinable()) {
    workerThread_->join();
  }
  workerThread_.reset();

  // Clean up pipeline components
  acquisitionNode_.reset();
  asyncBridge_.reset();
  demodNode_.reset();
  demodPassthrough_.reset();
  lineDetectionNode_.reset();

  initialized_ = false;
}

void VideoProcessor::start() {
  if (!initialized_) return;
  if (workerThread_) return;

  // Create worker thread with empty lambda function (placeholder for future processing logic)
  workerThread_ = std::make_unique<std::thread>([this]() {
    while (true) {
      if (!asyncBridge_->tick()) break;
    }
  });

  while (true) {
    if (!acquisitionNode_->tick()) break;
  }

  // Wait for the worker thread to complete (join)
  if (workerThread_->joinable()) {
    workerThread_->join();
  }
  std::cout << "a after join = " << std::endl; // should print 99
}

} // namespace IQVideoProcessor
