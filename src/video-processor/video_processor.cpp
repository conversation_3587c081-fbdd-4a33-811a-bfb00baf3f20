#include <algorithm>
#include "./video_processor.h"
#include "./video_processor_configs.h"
#include "./pipeline/iq_acquisition_node.h"
#include "./pipeline/iq_async_bridge.h"
#include "./pipeline/iq_demodulation_node.h"
#include "./pipeline/line_detection_node.h"
#include "../stream-pipeline/passthrough_link.h"
#include "../configs.h"
#include "../types.h"


class IIQStream;
namespace IQVideoProcessor {

/**
 * HELPER FUNCTIONS
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 */

size_t calcProcessingChunkSize(const SampleRateType /*sampleRate*/) {
  // Keep read window size small to ensure timely output in unit tests
  return 1024; // samples
}

size_t calcProcessingOverlapSize(const SampleRateType /*sampleRate*/) {
  // Left overlap used per side; total overlap = 2x this value
  return 128; // samples
}

/**
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 */

VideoProcessor::VideoProcessor(std::unique_ptr<IIQStream> stream) : stream_(std::move(stream)), initialized_(false) {

}

VideoProcessor::~VideoProcessor() {
  shutdown();
}

bool VideoProcessor::initialize() {
  if (initialized_) {
    return true;
  }

  if (!stream_) return false;
  if (sampleRate_ = stream_->sampleRate(); sampleRate_ == 0) return false;

  // Calculating the maximum samples per video line, required to pre-allocate buffers and other computations
  maxVideoLineSamples_ = static_cast<size_t>(sampleRate_ / MIN_LINE_RATE_HZ);
  if (maxVideoLineSamples_ < MIN_SAMPLES_PER_VIDEO_LINE) return false;

  // Calculate the desired amount of processable samples and overlap per window
  auto effectiveSamples = static_cast<size_t>(maxVideoLineSamples_ * LINES_PER_CHUNK); // ~20 lines
  auto leftOverlap = maxVideoLineSamples_; // one line of overlap on each side

  Pipeline::IQAcquisitionNode acquisitionNode_1(std::move(stream_), IQ_STREAM_READ_SIZE, effectiveSamples, leftOverlap);
  Pipeline::IQAsyncBridge asyncBridge_1(100);
  Pipeline::IQDemodulationNode demodNode_1;
  SPipeline::PassthroughLink<Pipeline::DemodulatedSegment*> demodPassthrough_1;
  Pipeline::LineDetectionNode lineDetectionNode_1;

  // Connect acquisition to demodulation through async bridge (cross-thread)
  acquisitionNode_1.connectOutputLink(&asyncBridge_1);
  demodNode_1.connectInputLink(&asyncBridge_1);

  // Replace DemodAsyncBridge with direct passthrough to line detection (same-thread)
  demodNode_1.connectOutputLink(&demodPassthrough_1);
  lineDetectionNode_1.connectInputLink(&demodPassthrough_1);


  initialized_ = true;
  return true;
}

void VideoProcessor::shutdown() {
  if (!initialized_) {
    return;
  }

  initialized_ = false;
}

} // namespace IQVideoProcessor
