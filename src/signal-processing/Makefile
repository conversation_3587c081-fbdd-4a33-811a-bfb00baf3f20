# Makefile for Signal-Processing module tests

CXX ?= g++
CXXFLAGS ?= -std=c++17 -Wall -Wextra -O2 -g -pedantic
INCLUDES = -I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline

TEST_SOURCES = find_front_frwd.cpp tests/test_find_front_frwd.cpp
TEST_EXECUTABLE = find_front_frwd_tests

FRONT_DETECTION_SOURCES = find_front_frwd.cpp tests/test_front_detection.cpp
FRONT_DETECTION_EXECUTABLE = front_detection_tests

all: test

$(TEST_EXECUTABLE): $(TEST_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

$(FRONT_DETECTION_EXECUTABLE): $(FRONT_DETECTION_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

build: $(TEST_EXECUTABLE) $(FRONT_DETECTION_EXECUTABLE)

test: $(TEST_EXECUTABLE) $(FRONT_DETECTION_EXECUTABLE)
	./$(TEST_EXECUTABLE)
	./$(FRONT_DETECTION_EXECUTABLE)

clean:
	rm -f $(TEST_EXECUTABLE) $(FRONT_DETECTION_EXECUTABLE) tests/*.o *.o

.PHONY: all build test clean

