#pragma once

#include <vector>
#include "../types.h"

namespace IQVideoProcessor::SignalProcessing {

template <typename TFloat = float>
class FindFrontFrwd {
public:
  // Constructor parameter reorganization: data pointer and effective length
  // are immutable for the finder instance and come before the threshold ring.
  explicit FindFrontFrwd(const TFloat* data,
                         std::size_t effectiveLen,
                         std::vector<TFloat>& thresholdRing);

  // Returns true on successful execution (not necessarily detection).
  bool operator()(bool toEOF,
                  int frontType,
                  TFloat dblFromPosition,
                  TFloat dblElements,
                  TFloat dblAveSize,
                  TFloat dblThresholdSize,
                  TFloat dblThresholdTrigDelta,
                  TFloat& frontPos,
                  bool& frontFound) const;

private:
  const TFloat* data_{nullptr};
  std::size_t effectiveLen_{0};
  std::vector<TFloat>& thresholdRing_; // external ring buffer with capacity >= thresholdSize
};

} // namespace IQVideoProcessor::SignalProcessing


