#!/bin/bash
set -e

CXX=${CXX:-g++}
CXXFLAGS="-std=c++17 -Wall -Wextra -O2 -g -pedantic"
INCLUDES="-I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline"

SRC_DIR=$(cd "$(dirname "$0")" && pwd)
cd "$SRC_DIR"

echo "SignalProcessing Test Runner"
echo "============================"

$CXX $CXXFLAGS $INCLUDES find_front_frwd.cpp tests/test_find_front_frwd.cpp -o find_front_frwd_tests
$CXX $CXXFLAGS $INCLUDES find_front_frwd.cpp tests/test_front_detection.cpp -o front_detection_tests

echo "Running general algorithm tests..."
./find_front_frwd_tests

echo ""
echo "Running front detection tests..."
./front_detection_tests

echo "All SignalProcessing tests completed."

